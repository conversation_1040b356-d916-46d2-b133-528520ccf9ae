# 🎵 دليل التشغيل في الخلفية - Background Audio Guide

## ✅ التحسينات المطبقة

تم تطبيق التحسينات التالية لضمان استمرار تشغيل الراديو في الخلفية:

### 🔧 1. معالج حالة التطبيق (AppState Handler)

```javascript
// معالجة تغيير حالة التطبيق
useEffect(() => {
  const handleAppStateChange = (nextAppState) => {
    if (nextAppState === "background" || nextAppState === "inactive") {
      // التطبيق ينتقل للخلفية - الحفاظ على التشغيل
      if (sound && isPlaying) {
        console.log("App going to background, maintaining audio playback");
      }
    } else if (nextAppState === "active") {
      // التطبيق يعود للمقدمة - فحص حالة الصوت
      if (sound && userInitiated && !manuallyPaused) {
        setTimeout(async () => {
          const status = await sound.getStatusAsync();
          if (status.isLoaded && !status.isPlaying && !connectionError) {
            handleStart(); // إعادة تشغيل البث
          }
        }, 1000);
      }
    }
  };

  const subscription = AppState.addEventListener(
    "change",
    handleAppStateChange
  );
  return () => subscription?.remove();
}, [sound, isPlaying, userInitiated, manuallyPaused, connectionError]);
```

### 🎛️ 2. إعدادات الصوت المحسنة

```javascript
await Audio.setAudioModeAsync({
  staysActiveInBackground: true, // التشغيل في الخلفية
  playsInSilentModeIOS: true, // التشغيل في الوضع الصامت
  shouldDuckAndroid: false, // عدم تقليل الصوت
  interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
  interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
  allowsRecordingIOS: false, // لا نحتاج للتسجيل
  playThroughEarpieceAndroid: false, // استخدام السماعات
});
```

### 📱 3. إعدادات إنشاء الصوت

```javascript
const { sound: newSound } = await Audio.Sound.createAsync(
  { uri: STREAM_URL },
  {
    shouldPlay: true,
    progressUpdateIntervalMillis: 250,
    isLooping: false, // البث المباشر لا يتكرر
    volume: 1.0, // الصوت كاملاً
    isMuted: false,
    rate: 1.0,
    shouldCorrectPitch: false,
  }
);
```

### 🔒 4. إدارة Keep Awake الذكية

```javascript
useEffect(() => {
  if (isPlaying) {
    // تفعيل منع النوم فقط عندما يكون التطبيق في المقدمة
    const currentAppState = AppState.currentState;
    if (currentAppState === "active") {
      activateKeepAwakeAsync();
    }
  } else {
    deactivateKeepAwake();
  }
}, [isPlaying]);
```

### 📋 5. الصلاحيات المطلوبة (app.json)

```json
{
  "android": {
    "permissions": [
      "INTERNET",
      "ACCESS_NETWORK_STATE",
      "WAKE_LOCK",
      "FOREGROUND_SERVICE", // جديد: للخدمات في المقدمة
      "MODIFY_AUDIO_SETTINGS" // جديد: لتعديل إعدادات الصوت
    ]
  },
  "ios": {
    "infoPlist": {
      "UIBackgroundModes": ["audio", "background-processing"]
    }
  }
}
```

## 🎯 كيف تعمل الميزة

### 📱 عند قفل الشاشة:

1. التطبيق ينتقل لحالة `background`
2. الصوت يستمر في التشغيل بفضل `staysActiveInBackground: true`
3. يتم إلغاء تفعيل منع النوم لتوفير البطارية

### 🔄 عند فتح التطبيق:

1. التطبيق يعود لحالة `active`
2. يتم فحص حالة الصوت
3. إذا توقف الصوت، يتم إعادة تشغيله تلقائياً
4. يتم تفعيل منع النوم مرة أخرى

### 🔀 عند تبديل التطبيقات:

1. التطبيق ينتقل لحالة `inactive` ثم `background`
2. الصوت يستمر في التشغيل
3. عند العودة، يتم فحص الحالة وإعادة التشغيل إذا لزم الأمر

## 🧪 اختبار الميزة

### ✅ اختبارات مطلوبة:

1. **قفل الشاشة:**

   - شغل الراديو
   - اقفل الشاشة
   - تأكد من استمرار الصوت

2. **تبديل التطبيقات:**

   - شغل الراديو
   - اذهب لتطبيق آخر
   - ارجع للراديو
   - تأكد من استمرار التشغيل

3. **انقطاع الإنترنت:**
   - شغل الراديو
   - اقطع الإنترنت لثوانٍ
   - أعد تشغيل الإنترنت
   - تأكد من إعادة الاتصال التلقائي

## 🚀 للنشر

هذه التحسينات تعمل في:

- ✅ **Development Build** (EAS Build)
- ✅ **Production APK/AAB**
- ⚠️ **Expo Go** (محدود بسبب قيود SDK 53+)

للحصول على أفضل تجربة، استخدم Development Build أو Production Build.

## 🔒 أدوات التحكم في شاشة القفل

### ✅ الميزات المضافة:

```javascript
// إعداد فئات الإشعارات
await Notifications.setNotificationCategoryAsync("MEDIA_CONTROLS", [
  {
    identifier: "PLAY_ACTION",
    buttonTitle: "▶️ Çal",
    options: { opensAppToForeground: false },
  },
  {
    identifier: "PAUSE_ACTION",
    buttonTitle: "⏸️ Durdur",
    options: { opensAppToForeground: false },
  },
  {
    identifier: "REFRESH_ACTION",
    buttonTitle: "🔄 Yenile",
    options: { opensAppToForeground: false },
  },
]);
```

### 🎵 إشعار التحكم في الوسائط:

```javascript
const updateMediaNotification = async (playing) => {
  await Notifications.scheduleNotificationAsync({
    content: {
      title: "Radyo Yakamoz",
      subtitle: "Canlı Yayın",
      body: playing ? "Şu anda çalıyor..." : "Duraklatıldı",
      sound: false,
      priority: Notifications.AndroidNotificationPriority.LOW,
      sticky: true,
      categoryIdentifier: "MEDIA_CONTROLS",
    },
    trigger: null,
    identifier: "MEDIA_NOTIFICATION",
  });
};
```

### 📱 كيف تعمل أدوات التحكم:

1. **عند قفل الشاشة:** يظهر إشعار مع أزرار التحكم
2. **أزرار التحكم المتاحة:**
   - ▶️ **Çal** - تشغيل الراديو
   - ⏸️ **Durdur** - إيقاف مؤقت
   - 🔄 **Yenile** - إعادة تحميل البث
3. **بدون فتح التطبيق:** يمكن التحكم مباشرة من الإشعار

## 🖼️ تحسينات Splash Screen

### الإعدادات المحسنة:

```json
{
  "expo-splash-screen": {
    "image": "./assets/images/splash.png",
    "resizeMode": "contain",
    "backgroundColor": "#000000",
    "imageWidth": 300,
    "imageHeight": 300
  }
}
```

- ✅ **resizeMode: "contain"** - يحافظ على نسبة الصورة
- ✅ **imageWidth & imageHeight: 300** - حجم أكبر وأوضح
- ✅ **backgroundColor: "#000000"** - خلفية سوداء متناسقة

## 📝 ملاحظات مهمة

- التشغيل في الخلفية يعمل فقط مع البناء الإنتاجي أو Development Build
- أدوات التحكم في شاشة القفل تعمل مع Development Build و Production Build
- في Expo Go، قد تكون هناك قيود على الإشعارات والتشغيل في الخلفية
- تأكد من اختبار الميزة على جهاز حقيقي وليس المحاكي
- البطارية قد تستنزف أسرع مع التشغيل المستمر في الخلفية
