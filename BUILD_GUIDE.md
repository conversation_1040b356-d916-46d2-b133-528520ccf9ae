# دليل بناء APK - APK Build Guide

دليل شامل لبناء ملف APK لتطبيق راديو يقموز.

## 🚀 الطرق المتاحة لبناء APK

### الطريقة 1: استخدام EAS Build (الموصى بها)

#### المتطلبات
```bash
# تثبيت EAS CLI
npm install -g @expo/eas-cli

# تسجيل الدخول إلى Expo
eas login
```

#### خطوات البناء
```bash
# 1. تكوين المشروع
eas build:configure

# 2. بناء APK للاختبار
eas build --platform android --profile preview

# 3. بناء APK للإنتاج
eas build --platform android --profile apk
```

### الطريقة 2: استخدام Expo CLI التقليدي

```bash
# بناء APK مباشرة
expo build:android -t apk

# بناء App Bundle للمتجر
expo build:android -t app-bundle
```

## 🔧 إعدادات البناء المحسنة

### ملف eas.json
```json
{
  "build": {
    "preview": {
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleRelease"
      }
    },
    "apk": {
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleRelease"
      }
    }
  }
}
```

### ملف app.json - إعدادات Android
```json
{
  "android": {
    "package": "com.yakamoz.radio",
    "versionCode": 1,
    "compileSdkVersion": 34,
    "targetSdkVersion": 34,
    "minSdkVersion": 21,
    "permissions": [
      "INTERNET",
      "ACCESS_NETWORK_STATE", 
      "WAKE_LOCK",
      "FOREGROUND_SERVICE",
      "MODIFY_AUDIO_SETTINGS"
    ],
    "usesCleartextTraffic": true
  }
}
```

## 🛠️ حل مشاكل البناء الشائعة

### مشكلة: "App not supported"
**الحل:**
1. تحديث `targetSdkVersion` إلى 34
2. إضافة `compileSdkVersion: 34`
3. التأكد من `minSdkVersion: 21`

### مشكلة: مشاكل الشبكة في APK
**الحل:**
1. إضافة `usesCleartextTraffic: true`
2. تكوين `network_security_config.xml`
3. إضافة الصلاحيات المطلوبة

### مشكلة: مشاكل الصوت
**الحل:**
1. إضافة `MODIFY_AUDIO_SETTINGS` permission
2. إضافة `FOREGROUND_SERVICE` permission
3. تكوين `UIBackgroundModes` لـ iOS

## 📱 اختبار APK

### قبل النشر
```bash
# اختبار على جهاز حقيقي
adb install app-release.apk

# اختبار الوظائف الأساسية:
# ✅ تشغيل الراديو
# ✅ إيقاف/تشغيل
# ✅ عمل التطبيق في الخلفية
# ✅ إعادة الاتصال عند انقطاع الشبكة
```

### فحص الأداء
```bash
# فحص استهلاك الذاكرة
adb shell dumpsys meminfo com.yakamoz.radio

# فحص استهلاك البطارية
adb shell dumpsys batterystats com.yakamoz.radio
```

## 🔐 التوقيع والأمان

### إنشاء مفتاح التوقيع
```bash
# إنشاء keystore جديد
keytool -genkey -v -keystore yakamoz-radio.keystore -alias yakamoz-key -keyalg RSA -keysize 2048 -validity 10000

# التوقيع اليدوي
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore yakamoz-radio.keystore app-unsigned.apk yakamoz-key
```

### تحسين APK
```bash
# ضغط وتحسين APK
zipalign -v 4 app-unsigned.apk app-release.apk
```

## 📊 تحسين حجم APK

### تقليل حجم التطبيق
```json
// في app.json
{
  "expo": {
    "assetBundlePatterns": [
      "assets/images/*",
      "assets/fonts/*"
    ]
  }
}
```

### إزالة التبعيات غير المستخدمة
```bash
# تحليل حجم Bundle
npx expo install --fix
npx react-native-bundle-visualizer
```

## 🚀 النشر

### رفع إلى Google Play Store
1. إنشاء App Bundle بدلاً من APK
2. رفع إلى Google Play Console
3. اختبار Internal Testing
4. النشر للجمهور

### التوزيع المباشر
1. رفع APK إلى خدمة استضافة
2. إنشاء صفحة تحميل
3. إضافة تعليمات التثبيت

## 📋 قائمة مراجعة قبل النشر

### الوظائف
- [ ] تشغيل الراديو يعمل بشكل صحيح
- [ ] إيقاف/تشغيل يعمل
- [ ] إعادة الاتصال التلقائية تعمل
- [ ] التطبيق يعمل في الخلفية
- [ ] مؤشرات الحالة تعمل

### الأداء
- [ ] لا توجد تسريبات في الذاكرة
- [ ] استهلاك البطارية معقول
- [ ] سرعة بدء التشغيل مقبولة
- [ ] لا توجد crashes

### التوافق
- [ ] يعمل على Android 5.0+ (API 21+)
- [ ] يعمل على أجهزة مختلفة
- [ ] يعمل مع اتصالات شبكة مختلفة
- [ ] يتعامل مع انقطاع الشبكة

## 🆘 الدعم الفني

### في حالة مشاكل البناء
1. تنظيف الكاش: `expo start --clear`
2. إعادة تثبيت التبعيات: `rm -rf node_modules && npm install`
3. تحديث Expo: `expo install --fix`

### لوجات مفيدة
```bash
# عرض لوجات البناء
eas build --platform android --profile preview --local

# عرض لوجات الجهاز
adb logcat | grep -i yakamoz
```

---

**ملاحظة**: هذا الدليل يتم تحديثه باستمرار مع تطور أدوات البناء.
