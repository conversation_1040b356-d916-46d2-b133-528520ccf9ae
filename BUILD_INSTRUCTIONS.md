# 🚀 تعليمات بناء APK - Build Instructions

## ⚡ البناء السريع

### 1. التحضير
```bash
# تأكد من تثبيت EAS CLI
npm install -g @expo/eas-cli

# تسجيل الدخول
eas login
```

### 2. البناء للاختبار
```bash
# بناء APK للاختبار
eas build --platform android --profile preview
```

### 3. البناء للإنتاج
```bash
# بناء APK للنشر
eas build --platform android --profile production
```

## 📋 قائمة مراجعة قبل البناء

### ✅ تم التحقق من:
- [x] إزالة expo-notifications
- [x] تنظيف جميع الاستيرادات غير المستخدمة
- [x] اختبار التطبيق في Expo Go
- [x] التأكد من عمل التشغيل في الخلفية
- [x] فحص جميع الميزات

### ✅ الإعدادات الصحيحة:
- [x] targetSdkVersion: 34
- [x] compileSdkVersion: 34
- [x] minSdkVersion: 21
- [x] جميع الصلاحيات مطلوبة
- [x] usesCleartextTraffic: true

## 🔧 إعدادات البناء

### ملف eas.json (موجود ومحسن)
```json
{
  "build": {
    "preview": {
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "apk"
      }
    }
  }
}
```

### ملف app.json (محسن ومراجع)
```json
{
  "android": {
    "package": "com.yakamoz.radio",
    "versionCode": 1,
    "compileSdkVersion": 34,
    "targetSdkVersion": 34,
    "minSdkVersion": 21,
    "permissions": [
      "INTERNET",
      "ACCESS_NETWORK_STATE",
      "WAKE_LOCK",
      "FOREGROUND_SERVICE",
      "MODIFY_AUDIO_SETTINGS"
    ],
    "usesCleartextTraffic": true
  }
}
```

## 🧪 اختبار APK

### بعد البناء:
```bash
# تحميل APK من EAS
# تثبيت على جهاز حقيقي
adb install app-release.apk

# اختبار الوظائف:
# ✅ تشغيل الراديو
# ✅ قفل الشاشة (يجب أن يستمر الصوت)
# ✅ تبديل التطبيقات (يجب أن يستمر الصوت)
# ✅ قطع الإنترنت وإعادته (إعادة اتصال تلقائية)
```

## 🚨 في حالة مشاكل البناء

### تنظيف المشروع:
```bash
# تنظيف الكاش
expo start --clear

# إعادة تثبيت التبعيات
rm -rf node_modules
npm install

# تحديث Expo
expo install --fix
```

### فحص الأخطاء:
```bash
# عرض لوجات البناء
eas build --platform android --profile preview --local

# فحص التبعيات
npm audit

# فحص التوافق
expo doctor
```

## 📱 النشر

### Google Play Store:
1. ارفع APK إلى Google Play Console
2. املأ معلومات التطبيق
3. اختبر في Internal Testing
4. انشر للجمهور

### التوزيع المباشر:
1. ارفع APK إلى خدمة استضافة
2. أنشئ رابط تحميل
3. أضف تعليمات التثبيت

## 🎯 نصائح مهمة

### ✅ افعل:
- اختبر على جهاز حقيقي
- تأكد من اتصال الإنترنت
- اختبر التشغيل في الخلفية
- راجع جميع الميزات

### ❌ لا تفعل:
- لا تضيف تبعيات جديدة بدون اختبار
- لا تغير إعدادات الصوت
- لا تحذف الصلاحيات المطلوبة
- لا تنس اختبار APK قبل النشر

## 🆘 الدعم

### في حالة المشاكل:
1. راجع `APK_BUILD_SAFETY_REPORT.md`
2. تأكد من اتباع هذه التعليمات
3. اختبر في Expo Go أولاً
4. استخدم `expo doctor` للفحص

---

**✅ الكود جاهز للبناء بأمان تام!**

**🚀 يمكنك الآن بناء APK بثقة كاملة!**
