# 🚀 دليل النشر - Yakamoz Radio App

دليل شامل لنشر تطبيق راديو يقموز على Google Play Store و Apple App Store.

## 📋 المتطلبات الأساسية

### للأندرويد (Google Play)
- حساب Google Play Developer (25$ رسوم لمرة واحدة)
- Android Studio مثبت
- Java Development Kit (JDK) 11+
- Android SDK

### للـ iOS (App Store)
- حساب Apple Developer (99$ سنوياً)
- macOS مع Xcode
- iOS Simulator أو جهاز iOS للاختبار

## 🔧 إعداد البيئة

### 1. تثبيت التبعيات الجديدة

```bash
# تثبيت react-native-track-player
npm install react-native-track-player

# تثبيت expo-haptics للردود اللمسية
npm install expo-haptics

# تثبيت expo-keep-awake لمنع إطفاء الشاشة
npm install expo-keep-awake
```

### 2. تكوين TrackPlayer

تأكد من وجود ملف `services/TrackPlayerService.js` مع الإعدادات الصحيحة:

```javascript
// خدمة TrackPlayer للتشغيل في الخلفية
import TrackPlayer, { Event } from 'react-native-track-player';

module.exports = async function() {
  TrackPlayer.addEventListener(Event.RemotePlay, () => TrackPlayer.play());
  TrackPlayer.addEventListener(Event.RemotePause, () => TrackPlayer.pause());
  TrackPlayer.addEventListener(Event.RemoteStop, () => TrackPlayer.destroy());
};
```

## 📱 بناء التطبيق للأندرويد

### 1. إعداد EAS Build

```bash
# تثبيت EAS CLI
npm install -g @expo/eas-cli

# تسجيل الدخول
eas login

# تكوين المشروع
eas build:configure
```

### 2. بناء APK للاختبار

```bash
# بناء APK للاختبار
eas build --platform android --profile preview
```

### 3. بناء AAB للنشر

```bash
# بناء AAB للنشر على Google Play
eas build --platform android --profile production
```

## 🍎 بناء التطبيق للـ iOS

### 1. إعداد الشهادات

```bash
# إنشاء الشهادات تلقائياً
eas credentials
```

### 2. بناء للـ iOS

```bash
# بناء للـ iOS
eas build --platform ios --profile production
```

## 🔐 الإعدادات الأمنية المطلوبة

### Android - network_security_config.xml

```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">radyotvonline.com</domain>
    </domain-config>
</network-security-config>
```

### iOS - Info.plist

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>radyotvonline.com</key>
        <dict>
            <key>NSExceptionAllowsInsecureHTTPLoads</key>
            <true/>
        </dict>
    </dict>
</dict>
```

## 📊 اختبار الجودة

### 1. اختبارات الأداء

- ✅ اختبار التشغيل في الخلفية
- ✅ اختبار إعادة الاتصال التلقائي
- ✅ اختبار استهلاك البطارية
- ✅ اختبار الذاكرة والأداء

### 2. اختبارات التوافق

- ✅ Android 5.0+ (API 21+)
- ✅ iOS 11.0+
- ✅ أحجام شاشات مختلفة
- ✅ اتجاهات الشاشة

## 🚀 النشر على المتاجر

### Google Play Store

1. **إنشاء التطبيق**
   - اذهب إلى Google Play Console
   - أنشئ تطبيق جديد
   - املأ المعلومات الأساسية

2. **رفع AAB**
   - اذهب إلى "App bundles"
   - ارفع ملف AAB المبني
   - املأ معلومات الإصدار

3. **إعداد المتجر**
   - أضف الوصف والصور
   - حدد الفئة والتصنيف
   - أضف سياسة الخصوصية

### Apple App Store

1. **App Store Connect**
   - أنشئ تطبيق جديد
   - املأ المعلومات الأساسية

2. **رفع البناء**
   - استخدم Xcode أو Transporter
   - ارفع ملف IPA

3. **مراجعة التطبيق**
   - املأ معلومات المراجعة
   - أرسل للمراجعة

## ⚠️ نصائح مهمة

### للأندرويد
- تأكد من توقيع التطبيق بشهادة صحيحة
- اختبر على أجهزة مختلفة
- تحقق من أذونات التطبيق

### للـ iOS
- اختبر على أجهزة حقيقية
- تأكد من إعدادات Background Modes
- راجع إرشادات App Store

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

**مشكلة**: فشل البناء
- **الحل**: تحقق من إعدادات app.json وتأكد من تثبيت جميع التبعيات

**مشكلة**: عدم عمل التشغيل في الخلفية
- **الحل**: تأكد من إعدادات UIBackgroundModes في iOS وأذونات Android

**مشكلة**: رفض من المتجر
- **الحل**: راجع إرشادات المتجر وتأكد من سياسة الخصوصية

## 📞 الدعم

للحصول على المساعدة:
- راجع وثائق Expo
- تحقق من مجتمع React Native
- استخدم GitHub Issues للمشاكل التقنية
