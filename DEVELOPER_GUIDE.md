# 👨‍💻 دليل المطور - Yakamoz Radio App

دليل شامل للمطورين للعمل على تطبيق راديو يقموز.

## 🏗️ بنية المشروع

```
yakamoz-radio/
├── app/
│   └── index.js                 # المكون الرئيسي للتطبيق
├── assets/
│   ├── images/
│   │   ├── headphones.png       # صورة السماعات
│   │   ├── mobileapp-yakamoz.png # شعار الراديو
│   │   ├── icon.png             # أيقونة التطبيق
│   │   └── splash-icon.png      # شاشة البداية
│   └── fonts/                   # الخطوط (إن وجدت)
├── services/
│   └── TrackPlayerService.js    # خدمة TrackPlayer
├── src/
│   └── network_security_config.xml # إعدادات أمان الشبكة
├── app.json                     # إعدادات Expo
├── eas.json                     # إعدادات EAS Build
├── package.json                 # التبعيات والسكريبتات
└── README.md                    # وثائق المشروع
```

## 🔧 التقنيات المستخدمة

### المكتبات الأساسية
- **React Native**: إطار العمل الأساسي
- **Expo**: منصة التطوير والنشر
- **React Native Track Player**: مشغل الصوت المتقدم

### مكتبات واجهة المستخدم
- **expo-blur**: تأثيرات الضبابية (Glassmorphism)
- **expo-linear-gradient**: التدرجات اللونية
- **react-native-svg**: الرسوم المتجهة
- **@expo/vector-icons**: الأيقونات

### مكتبات التفاعل
- **expo-haptics**: ردود الفعل اللمسية
- **expo-keep-awake**: منع إطفاء الشاشة

## 🎨 نمط التصميم

### Glassmorphism Design System

```javascript
// مكون البطاقة الزجاجية
const GlassCard = ({ children, style, intensity = 20 }) => {
  return (
    <View style={[styles.glassCard, style]}>
      <BlurView intensity={intensity} style={StyleSheet.absoluteFill} tint="dark" />
      {children}
    </View>
  );
};

// الأنماط الأساسية
const glassCard = {
  borderRadius: 20,
  borderWidth: 1,
  borderColor: "rgba(255, 255, 255, 0.1)",
  backgroundColor: "rgba(255, 255, 255, 0.05)",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.3,
  shadowRadius: 20,
  elevation: 10,
};
```

### نظام الألوان

```javascript
const colors = {
  primary: "#7C4DFF",      // البنفسجي الأساسي
  secondary: "#9668EF",    // البنفسجي الفاتح
  accent: "#a18aff",       // البنفسجي المتدرج
  success: "#4CAF50",      // الأخضر للتشغيل
  error: "#FF6B6B",        // الأحمر للأخطاء
  background: "#0A0A0A",   // الخلفية الداكنة
  surface: "rgba(255, 255, 255, 0.05)", // سطح البطاقات
  text: "#FFFFFF",         // النص الأبيض
  textSecondary: "#d1cfff" // النص الثانوي
};
```

## 🔊 إدارة الصوت

### TrackPlayer Setup

```javascript
const setupTrackPlayer = async () => {
  await TrackPlayer.setupPlayer({
    maxCacheSize: 1024 * 10, // 10MB cache
  });

  await TrackPlayer.updateOptions({
    capabilities: [
      Capability.Play,
      Capability.Pause,
      Capability.Stop,
    ],
    compactCapabilities: [Capability.Play, Capability.Pause],
    progressUpdateEventInterval: 2,
  });

  await TrackPlayer.add({
    id: "yakamoz-radio",
    url: "https://ssldyg.radyotvonline.com/smil/radyoyakamoz.stream/playlist.m3u8",
    title: "Radiyo Yakamoz",
    artist: "Canlı Yayın",
    isLiveStream: true,
  });
};
```

### إدارة الحالات

```javascript
// استخدام hooks للحالة
const playbackState = usePlaybackState();
const progress = useProgress();

// الحالات المشتقة
const isPlaying = playbackState === State.Playing;
const isPaused = playbackState === State.Paused;
const isBuffering = playbackState === State.Buffering;
const isError = playbackState === State.Error;
```

## 🎭 الرسوم المتحركة

### موجات الراديو

```javascript
const RadioWaves = ({ size = 300, color = "#7C4DFF", isPlaying = false }) => {
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;
  const wave3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isPlaying) {
      // إنشاء ثلاث موجات متتالية
      const animations = [wave1, wave2, wave3].map((wave, index) => 
        Animated.loop(
          Animated.timing(wave, {
            toValue: 1,
            duration: 2500,
            delay: index * 800,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          })
        )
      );
      
      animations.forEach(anim => anim.start());
      return () => animations.forEach(anim => anim.stop());
    }
  }, [isPlaying]);
};
```

### تأثير النبض للزر

```javascript
const pulseAnim = useRef(new Animated.Value(1)).current;

useEffect(() => {
  if (isPlaying) {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }
}, [isPlaying]);
```

## 🛠️ أفضل الممارسات

### إدارة الأخطاء

```javascript
const handleStart = useCallback(async () => {
  try {
    setIsLoading(true);
    setConnectionError(false);
    
    await TrackPlayer.play();
    
    // Haptic feedback
    if (Haptics) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  } catch (error) {
    console.warn("Stream start error:", error);
    setConnectionError(true);
    Alert.alert("خطأ", "لا يمكن تشغيل الراديو");
  } finally {
    setIsLoading(false);
  }
}, []);
```

### تحسين الأداء

```javascript
// استخدام useCallback للدوال
const togglePlayback = useCallback(async () => {
  // منطق التشغيل/الإيقاف
}, [isPlaying]);

// استخدام useMemo للحسابات المعقدة
const waveformBars = useMemo(() => {
  return [...Array(WAVE_BAR_COUNT)].map((_, i) => ({
    id: i,
    baseHeight: 15 + (i % 3) * 10,
  }));
}, []);
```

## 🧪 الاختبار

### اختبارات الوحدة

```bash
# تشغيل الاختبارات
npm test

# اختبار مع التغطية
npm run test:coverage
```

### اختبارات التكامل

```bash
# اختبار على الأندرويد
npm run android

# اختبار على iOS
npm run ios
```

## 📝 إرشادات المساهمة

### قواعد الكود

1. **استخدم TypeScript** للتحقق من الأنواع
2. **اتبع ESLint rules** للتنسيق
3. **أضف تعليقات** للكود المعقد
4. **اختبر التغييرات** قبل الإرسال

### Git Workflow

```bash
# إنشاء فرع جديد
git checkout -b feature/new-feature

# إضافة التغييرات
git add .
git commit -m "feat: add new feature"

# دفع التغييرات
git push origin feature/new-feature
```

## 🔍 تصحيح الأخطاء

### أدوات التصحيح

```bash
# تشغيل مع debugger
npm start -- --dev-client

# عرض logs
npx react-native log-android
npx react-native log-ios
```

### مشاكل شائعة

**مشكلة**: TrackPlayer لا يعمل
- **الحل**: تأكد من تسجيل الخدمة في app.json

**مشكلة**: الرسوم المتحركة بطيئة
- **الحل**: استخدم useNativeDriver: true

## 📚 مصادر إضافية

- [React Native Documentation](https://reactnative.dev/)
- [Expo Documentation](https://docs.expo.dev/)
- [Track Player Documentation](https://react-native-track-player.js.org/)
- [React Native Animations](https://reactnative.dev/docs/animations)
