# دليل التطوير - Development Guide

دليل شامل للمطورين للعمل على تطبيق راديو يقموز.

## 🏗️ بنية المشروع

### الملفات الرئيسية
```
son-ykm/
├── app/
│   └── index.js                 # الملف الرئيسي للتطبيق
├── assets/
│   ├── images/
│   │   ├── headphones.png       # صورة السماعات
│   │   ├── mobileapp-yakamoz.png # شعار الراديو
│   │   ├── icon.png             # أيقونة التطبيق
│   │   └── adaptive-icon.png    # أيقونة Android
│   └── fonts/                   # الخطوط (إن وجدت)
├── src/
│   └── network_security_config.xml # إعدادات أمان الشبكة
├── app.json                     # إعدادات Expo
├── package.json                 # التبعيات والسكريبتات
└── README.md                    # التوثيق الرئيسي
```

## 🔧 المكونات الرئيسية

### 1. RadioWaves Component
مكون الموجات الراديوية المتحركة:
```javascript
const RadioWaves = ({ size = 300, color = "#7C4DFF" }) => {
  // أنيميشن موجات الراديو
}
```

### 2. Audio Management
إدارة الصوت والتشغيل:
```javascript
const handleStart = useCallback(async () => {
  // منطق بدء تشغيل الراديو
}, [dependencies]);

const togglePlayback = useCallback(async () => {
  // منطق التحكم في التشغيل/الإيقاف
}, [dependencies]);
```

### 3. Waveform Visualization
عرض الموجات الصوتية:
```javascript
const renderWaveform = useCallback(() => {
  // رسم الموجات الصوتية المتحركة
}, [waveformAnim, isPlaying]);
```

## 🎨 نظام التصميم

### الألوان الرئيسية
```javascript
const colors = {
  primary: "#7C4DFF",      // البنفسجي الرئيسي
  secondary: "#4CAF50",    // الأخضر للتشغيل
  error: "#FF6B6B",        // الأحمر للأخطاء
  background: "#100A1C",   // الخلفية الداكنة
  text: "#FFFFFF",         // النص الأبيض
  textSecondary: "#d1cfff" // النص الثانوي
};
```

### الخطوط والأحجام
```javascript
const typography = {
  title: { fontSize: 32, fontWeight: "bold" },
  subtitle: { fontSize: 16 },
  button: { fontSize: 18, fontWeight: "600" },
  trackTitle: { fontSize: 28, fontWeight: "bold" },
  trackSubtitle: { fontSize: 18 }
};
```

## 🔄 إدارة الحالة

### متغيرات الحالة الرئيسية
```javascript
// حالة الصوت
const [sound, setSound] = useState(null);
const [isPlaying, setIsPlaying] = useState(false);
const [isLoading, setIsLoading] = useState(false);
const [connectionError, setConnectionError] = useState(false);

// حالة واجهة المستخدم
const [showPlayer, setShowPlayer] = useState(false);
```

### مراجع الأنيميشن
```javascript
const fadeOutAnim = useRef(new Animated.Value(1)).current;
const fadeInAnim = useRef(new Animated.Value(0)).current;
const headphoneAnim = useRef(new Animated.Value(0)).current;
const waveformAnim = useRef(new Animated.Value(0)).current;
```

## 🎵 إدارة الصوت

### إعدادات الصوت
```javascript
await Audio.setAudioModeAsync({
  staysActiveInBackground: true,      // التشغيل في الخلفية
  playsInSilentModeIOS: true,        // التشغيل في الوضع الصامت
  interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
  interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
  shouldDuckAndroid: false,          // عدم تقليل الصوت
});
```

### معالجة حالة التشغيل
```javascript
newSound.setOnPlaybackStatusUpdate((status) => {
  if (status.error) {
    console.warn("Playback error:", status.error);
    setIsPlaying(false);
    setConnectionError(true);
  } else if (status.isLoaded) {
    setIsPlaying(status.isPlaying);
    setConnectionError(false);
  }
});
```

## 🔒 الأمان والشبكة

### إعدادات أمان الشبكة
```xml
<!-- src/network_security_config.xml -->
<network-security-config>
  <base-config cleartextTrafficPermitted="false">
    <trust-anchors>
      <certificates src="system" />
    </trust-anchors>
  </base-config>
  <domain-config cleartextTrafficPermitted="true">
    <domain includeSubdomains="true">ssldyg.radyotvonline.com</domain>
    <domain includeSubdomains="true">radyotvonline.com</domain>
  </domain-config>
</network-security-config>
```

## 🧪 الاختبار والتطوير

### تشغيل التطبيق للتطوير
```bash
# تشغيل عادي
npm start

# تشغيل مع تنظيف الكاش
npm start -- --clear

# تشغيل على Android مباشرة
npm run android

# تشغيل على iOS مباشرة
npm run ios
```

### اختبار الوظائف
```bash
# اختبار الوحدة
npm test

# اختبار مع مراقبة التغييرات
npm test -- --watch
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة في التطوير

#### 1. مشكلة تشغيل الصوت
```javascript
// التحقق من حالة الصوت
const status = await sound.getStatusAsync();
console.log("Sound status:", status);
```

#### 2. مشاكل الأنيميشن
```javascript
// إعادة تعيين الأنيميشن
useEffect(() => {
  return () => {
    // تنظيف الأنيميشن عند إلغاء التحميل
    animation.stop();
  };
}, []);
```

#### 3. مشاكل الذاكرة
```javascript
// تنظيف الموارد
useEffect(() => {
  return () => {
    if (sound) {
      sound.unloadAsync().catch(console.warn);
    }
  };
}, [sound]);
```

## 📱 البناء للإنتاج

### إعداد البناء
```bash
# بناء للتطوير
expo build:android --type apk

# بناء للإنتاج
expo build:android --type app-bundle
```

### تحسين الأداء
```javascript
// استخدام useCallback للوظائف
const optimizedFunction = useCallback(() => {
  // منطق الوظيفة
}, [dependencies]);

// استخدام useMemo للقيم المحسوبة
const memoizedValue = useMemo(() => {
  return expensiveCalculation(data);
}, [data]);
```

## 📋 قائمة المراجعة للتطوير

### قبل الكوميت
- [ ] اختبار التطبيق على Android و iOS
- [ ] التحقق من عدم وجود console.log غير ضروري
- [ ] التحقق من تنظيف الذاكرة
- [ ] اختبار تشغيل/إيقاف الراديو
- [ ] اختبار معالجة الأخطاء

### قبل الإصدار
- [ ] اختبار شامل على أجهزة متعددة
- [ ] تحديث رقم الإصدار
- [ ] تحديث CHANGELOG.md
- [ ] اختبار البناء للإنتاج
- [ ] اختبار الأداء والذاكرة

---

**ملاحظة**: هذا الدليل يتم تحديثه باستمرار مع تطور المشروع.
