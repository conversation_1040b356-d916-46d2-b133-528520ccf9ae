# بدء سريع - Quick Start

دليل سريع لتشغيل تطبيق راديو يقموز.

## 🚀 تشغيل التطبيق للتطوير

### 1. تثبيت المتطلبات
```bash
# تأكد من وجود Node.js
node --version

# تثبيت التبعيات
npm install
```

### 2. تشغيل التطبيق
```bash
# تشغيل عادي
npm start

# تشغيل مع تنظيف الكاش
npm run start:clear
```

### 3. اختبار التطبيق
- امسح QR code بتطبيق Expo Go
- أو اضغط `a` لفتح Android emulator
- أو اضغط `w` لفتح في المتصفح

## 📱 بناء APK للهاتف

### الطريقة السريعة
```bash
# تثبيت EAS CLI
npm install -g @expo/eas-cli

# تسجيل الدخول
eas login

# بناء APK
eas build --platform android --profile preview
```

## ✅ التحقق من عمل التطبيق

### الوظائف الأساسية
1. **شاشة البداية**: يجب أن تظهر بوضوح مع أنيميشن الموجات
2. **زر "Dinlemeye Başla"**: يجب أن يعمل بدون مشاكل
3. **شاشة المشغل**: يجب أن تظهر بوضوح كامل (100% opacity)
4. **تشغيل الراديو**: يجب أن يبدأ التشغيل فوراً
5. **أزرار التحكم**: جميع الأزرار يجب أن تكون واضحة ومرئية

### اختبار الشبكة
- اختبر مع WiFi
- اختبر مع بيانات الهاتف
- اختبر انقطاع وإعادة الاتصال

## 🐛 حل المشاكل السريع

### مشكلة: شاشة المشغل غير واضحة
**تم الحل**: تم إزالة تأثير الشفافية وجعل العناصر تظهر بوضوح كامل

### مشكلة: APK لا يعمل على الهاتف
**الحل**: 
- تحديث إعدادات Android في app.json
- إضافة network security config
- إضافة الصلاحيات المطلوبة

### مشكلة: الراديو لا يشتغل
**الحل**:
- تحقق من اتصال الإنترنت
- اضغط زر إعادة التحميل
- أعد تشغيل التطبيق

## 📁 الملفات المهمة

```
son-ykm/
├── app/index.js              # الملف الرئيسي (محسن)
├── app.json                  # إعدادات التطبيق (محسن)
├── package.json              # التبعيات (منظف)
├── eas.json                  # إعدادات البناء
├── metro.config.js           # إعدادات Metro
├── babel.config.js           # إعدادات Babel
└── src/network_security_config.xml # أمان الشبكة
```

## 🎯 التحسينات المطبقة

### ✅ تم إصلاحها
- **مشكلة الشفافية**: شاشة المشغل تظهر بوضوح كامل
- **مشكلة APK**: إعدادات محسنة للتوافق مع جميع الأجهزة
- **تحسين الأداء**: كود منظف وأسرع
- **تحسين الذاكرة**: منع تسريب الذاكرة

### 🚀 ميزات جديدة
- **إعادة اتصال تلقائية**: في حالة انقطاع الشبكة
- **مؤشرات حالة**: عرض واضح لحالة الاتصال
- **معالجة أخطاء محسنة**: رسائل واضحة
- **تحسين UI**: تصميم موحد ومتسق

## 📞 الدعم

### إذا واجهت مشاكل:
1. تأكد من اتصال الإنترنت
2. أعد تشغيل التطبيق
3. امسح الكاش: `npm run start:clear`
4. أعد تثبيت التبعيات: `npm install`

### للمطورين:
- راجع `DEVELOPMENT.md` للتفاصيل التقنية
- راجع `BUILD_GUIDE.md` لبناء APK
- راجع `CHANGELOG.md` لسجل التغييرات

---

**التطبيق جاهز للاستخدام! 🎉**
