{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "son", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.yakamoz.radio", "buildNumber": "1.0.0", "splash": {"image": "./assets/images/splash.png", "resizeMode": "cover", "backgroundColor": "#000000"}, "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"radyotvonline.com": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSExceptionMinimumTLSVersion": "1.0", "NSIncludesSubdomains": true}}}, "UIBackgroundModes": ["audio", "background-processing"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#7C4DFF"}, "splash": {"image": "./assets/images/splash.png", "resizeMode": "cover", "backgroundColor": "#000000", "imageWidth": 1080, "imageHeight": 1920}, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "WAKE_LOCK", "FOREGROUND_SERVICE", "MODIFY_AUDIO_SETTINGS"], "package": "com.yakamoz.radio", "usesCleartextTraffic": true, "allowBackup": false, "versionCode": 1, "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21, "buildToolsVersion": "34.0.0"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash.png", "resizeMode": "cover", "backgroundColor": "#000000", "imageWidth": 1080, "imageHeight": 1920}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "2e216aac-26b4-4df2-86d8-b59904961cf6"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/2e216aac-26b4-4df2-86d8-b59904961cf6"}}}