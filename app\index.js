import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  SafeAreaView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  AppState,
  StatusBar,
  Modal,
} from "react-native";
import Svg, { Defs, RadialGradient, Stop, Rect } from "react-native-svg";
import { BlurView } from "expo-blur";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Audio } from "expo-av";
import * as Haptics from "expo-haptics";
import { activateKeepAwakeAsync, deactivateKeepAwake } from "expo-keep-awake";
// import * as Notifications from "expo-notifications"; // Will be enabled in production build
import { Easing } from "react-native";
import headphonesImg from "../assets/images/headphones.png";
import radioImg from "../assets/images/mobileapp-yakamoz.png";

const { width } = Dimensions.get("window");

const WAVE_BAR_COUNT = 20;

// S<PERSON>an listesi
const SLOGANS = ["🎵 <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> "];

const App = () => {
  // Audio state
  const [sound, setSound] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionError, setConnectionError] = useState(false);

  // UI state
  const [showPlayer, setShowPlayer] = useState(false);

  // Custom modal state
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState("");
  const [modalMessage, setModalMessage] = useState("");
  const [modalType, setModalType] = useState("info"); // "info", "error", "success"

  // Slogan state
  const [currentSloganIndex, setCurrentSloganIndex] = useState(0);

  // Auto-reconnection state
  const [retryCount, setRetryCount] = useState(0);
  const [autoReconnectEnabled, setAutoReconnectEnabled] = useState(true);
  const [userInitiated, setUserInitiated] = useState(false); // Track if user manually started
  const [manuallyPaused, setManuallyPaused] = useState(false); // Track if user manually paused
  const retryTimeoutRef = useRef(null);

  // Animation refs
  const fadeOutAnim = useRef(new Animated.Value(1)).current;
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const headphoneAnim = useRef(new Animated.Value(0)).current;

  const pulseAnim = useRef(new Animated.Value(1)).current;
  const modalScaleAnim = useRef(new Animated.Value(0)).current;
  const sloganScrollAnim = useRef(new Animated.Value(0)).current;

  // Stream URL
  const STREAM_URL =
    "https://ssldyg.radyotvonline.com/smil/radyoyakamoz.stream/playlist.m3u8";

  // Audio and Media Session configuration
  useEffect(() => {
    const configureAudio = async () => {
      try {
        // Configure audio settings for background playback
        await Audio.setAudioModeAsync({
          staysActiveInBackground: true, // Keep audio active in background
          playsInSilentModeIOS: true, // Play even when device is in silent mode
          shouldDuckAndroid: false, // Don't lower volume for other apps
          interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
          interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
          allowsRecordingIOS: false, // We don't need recording
          playThroughEarpieceAndroid: false, // Use speakers/headphones
        });

        // Note: Lock screen controls will be enabled in production build
        // For now, background audio playback is working with expo-av

        console.log(
          "Audio and notification configuration completed for background playback"
        );
      } catch (error) {
        console.warn("Audio/Media configuration error:", error);
        setConnectionError(true);
      }
    };

    configureAudio();

    // Cleanup function
    return () => {
      if (sound) {
        sound.unloadAsync().catch(console.warn);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      // Note: Notification cleanup will be added in production build
    };
  }, [sound]);

  // Headphone float animation
  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(headphoneAnim, {
          toValue: 1,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(headphoneAnim, {
          toValue: 0,
          duration: 2000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [headphoneAnim]);

  // Pulse animation for play button
  useEffect(() => {
    if (isPlaying) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isPlaying, pulseAnim]);

  // Keep screen awake when playing (but allow background playback)
  useEffect(() => {
    if (isPlaying) {
      // Keep screen awake only when app is in foreground
      const currentAppState = AppState.currentState;
      if (currentAppState === "active") {
        activateKeepAwakeAsync();
        console.log("Screen keep awake activated");
      }
    } else {
      deactivateKeepAwake();
      console.log("Screen keep awake deactivated");
    }
  }, [isPlaying]);

  // Individual bar animations - each bar animates independently
  const [barAnimations] = useState(() =>
    Array.from({ length: WAVE_BAR_COUNT }, () => new Animated.Value(0.3))
  );

  useEffect(() => {
    const animations = [];

    const createIndividualBarAnimation = (barIndex) => {
      if (!isPlaying) return;

      // Each bar has different random timing
      const randomDelay = Math.random() * 2000; // 0-2 second delay
      const randomDuration = 400 + Math.random() * 800; // 400-1200ms duration
      const randomHeight = 0.3 + Math.random() * 0.7; // 30%-100% height

      const animation = Animated.sequence([
        Animated.delay(randomDelay),
        Animated.timing(barAnimations[barIndex], {
          toValue: randomHeight,
          duration: randomDuration,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true, // Use native driver for better performance
        }),
        Animated.timing(barAnimations[barIndex], {
          toValue: 0.3 + Math.random() * 0.4, // Random return height
          duration: randomDuration,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ]);

      animation.start((finished) => {
        if (finished && isPlaying) {
          // Restart animation for this bar
          createIndividualBarAnimation(barIndex);
        }
      });

      animations.push(animation);
    };

    if (isPlaying) {
      // Start animation for each bar independently
      for (let i = 0; i < WAVE_BAR_COUNT; i++) {
        createIndividualBarAnimation(i);
      }
    } else {
      // Reset all bars to minimum height
      barAnimations.forEach((anim) => anim.setValue(0.3));
    }

    return () => {
      animations.forEach((anim) => anim.stop());
    };
  }, [isPlaying, barAnimations]);

  // Slogan scroll animation - infinite loop with proper timing
  useEffect(() => {
    let animationTimeout;

    const startSloganAnimation = () => {
      // Reset position to start from right
      sloganScrollAnim.setValue(width + 100);

      Animated.timing(sloganScrollAnim, {
        toValue: -width * 2, // Move completely off screen
        duration: 15000, // 15 seconds for full scroll
        easing: Easing.linear,
        useNativeDriver: true,
      }).start((finished) => {
        if (finished) {
          // Wait before changing to next slogan
          animationTimeout = setTimeout(() => {
            setCurrentSloganIndex((prev) => (prev + 1) % SLOGANS.length);
            // Small delay before restarting animation
            setTimeout(() => {
              startSloganAnimation();
            }, 200);
          }, 1000); // 1 second pause between slogans
        }
      });
    };

    if (showPlayer) {
      // Start first animation after a small delay
      const initialTimeout = setTimeout(() => {
        startSloganAnimation();
      }, 1000);

      return () => {
        clearTimeout(initialTimeout);
        if (animationTimeout) clearTimeout(animationTimeout);
        sloganScrollAnim.stopAnimation();
      };
    }

    return () => {
      if (animationTimeout) clearTimeout(animationTimeout);
      sloganScrollAnim.stopAnimation();
    };
  }, [showPlayer, sloganScrollAnim]); // Removed currentSloganIndex dependency

  // Auto-reconnection logic - only if user has manually started the radio
  useEffect(() => {
    if (
      connectionError &&
      autoReconnectEnabled &&
      !isLoading &&
      userInitiated &&
      !manuallyPaused
    ) {
      const retryDelay = Math.min(3000 * Math.pow(1.5, retryCount), 30000); // More conservative: start with 3s, max 30s

      retryTimeoutRef.current = setTimeout(() => {
        console.log(`Auto-reconnecting... Attempt ${retryCount + 1}`);
        setRetryCount((prev) => prev + 1);
        handleStart();
      }, retryDelay);

      return () => {
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
        }
      };
    }
  }, [
    connectionError,
    autoReconnectEnabled,
    isLoading,
    retryCount,
    userInitiated,
    manuallyPaused,
  ]);

  // Reset retry count on successful connection and update media session
  useEffect(() => {
    const updateMediaState = async () => {
      if (isPlaying && !connectionError) {
        setRetryCount(0);
      }

      // Note: Media notification update will be added in production build
    };

    updateMediaState();
  }, [isPlaying, connectionError, userInitiated]);

  // Handle app state changes for background audio
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      console.log("App state changed to:", nextAppState);

      if (nextAppState === "background" || nextAppState === "inactive") {
        // App is going to background - ensure audio continues
        if (sound && isPlaying) {
          console.log("App going to background, maintaining audio playback");
          // Audio should continue playing due to staysActiveInBackground: true
        }
      } else if (nextAppState === "active") {
        // App is coming to foreground - check audio status
        if (sound && userInitiated && !manuallyPaused) {
          console.log("App coming to foreground, checking audio status");

          // Check if audio is still playing after a short delay
          setTimeout(async () => {
            try {
              const status = await sound.getStatusAsync();
              if (status.isLoaded && !status.isPlaying && !connectionError) {
                console.log(
                  "Audio stopped while in background, attempting to resume"
                );
                setIsPlaying(false); // Update UI state first
                handleStart(); // Restart the stream
              }
            } catch (error) {
              console.warn("Error checking audio status on foreground:", error);
            }
          }, 1000);
        }
      }
    };

    const subscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      subscription?.remove();
    };
  }, [sound, isPlaying, userInitiated, manuallyPaused, connectionError]);

  // Note: Lock screen controls will be implemented in production build

  // Auto-reconnection for paused state (only if not manually paused and user initiated)
  useEffect(() => {
    if (
      !isPlaying &&
      !isLoading &&
      !connectionError &&
      sound &&
      autoReconnectEnabled &&
      userInitiated &&
      !manuallyPaused
    ) {
      const checkConnection = async () => {
        try {
          const status = await sound.getStatusAsync();
          if (status.isLoaded && !status.isPlaying && !status.shouldPlay) {
            // Stream might be disconnected, try to reconnect
            console.log(
              "Detected unexpected paused state, attempting reconnection..."
            );
            startSloganAnimation();
            handleStart();
          }
        } catch (error) {
          console.warn("Connection check error:", error);
          setConnectionError(true);
        }
      };

      const checkTimeout = setTimeout(checkConnection, 10000); // Increased to 10 seconds to be less aggressive
      return () => clearTimeout(checkTimeout);
    }
  }, [
    isPlaying,
    isLoading,
    connectionError,
    sound,
    autoReconnectEnabled,
    userInitiated,
    manuallyPaused,
  ]);

  // Note: Media notification function will be implemented in production build

  // Custom modal functions
  const showCustomModal = useCallback(
    (title, message, type = "info") => {
      setModalTitle(title);
      setModalMessage(message);
      setModalType(type);
      modalScaleAnim.setValue(0); // Reset animation
      setModalVisible(true);

      // Animate modal in
      Animated.spring(modalScaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }).start();
    },
    [modalScaleAnim]
  );

  const hideCustomModal = useCallback(() => {
    // Animate modal out
    Animated.timing(modalScaleAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setModalVisible(false);
      setTimeout(() => {
        setModalTitle("");
        setModalMessage("");
        setModalType("info");
      }, 100);
    });
  }, [modalScaleAnim]);

  // Note: Media notifications removed due to Expo Go SDK 53+ limitations

  // Handle stream start with improved error handling
  const handleStart = useCallback(async () => {
    if (isLoading) {
      return;
    }
    try {
      setIsLoading(true);
      setConnectionError(false);
      setUserInitiated(true); // Mark as user initiated
      setManuallyPaused(false); // Reset manual pause flag

      // Clean up existing sound
      if (sound) {
        await sound.unloadAsync();
        setSound(null);
      }

      // Show player screen immediately
      if (!showPlayer) {
        setShowPlayer(true);
        fadeInAnim.setValue(1);

        Animated.timing(fadeOutAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }

      // Create and configure audio with background playback support
      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: STREAM_URL },
        {
          shouldPlay: true,
          progressUpdateIntervalMillis: 250, // Faster updates for better responsiveness
          isLooping: false, // Live stream doesn't loop
          volume: 1.0, // Full volume
          isMuted: false,
          rate: 1.0,
          shouldCorrectPitch: false,
        }
      );

      // Set up status listener with faster updates and background support
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.error) {
          console.warn("Playback error:", status.error);
          setIsPlaying(false);
          setConnectionError(true);
          setManuallyPaused(false); // ✅ هذا خطأ في الشبكة، ليس إيقاف يدوي
        } else if (status.isLoaded) {
          // Immediate state update for better responsiveness
          setIsPlaying(status.isPlaying);

          // ✅ إذا توقف التشغيل بدون خطأ وبدون إيقاف يدوي، فهو خطأ شبكة
          if (!status.isPlaying && !manuallyPaused && userInitiated) {
            console.warn(
              "Stream stopped unexpectedly, treating as connection error"
            );
            setConnectionError(true);
            setManuallyPaused(false); // تأكيد أنه ليس إيقاف يدوي
          } else if (status.isPlaying) {
            setConnectionError(false);
            console.log("Audio is playing - background playback active");
          }
        }
      });

      setSound(newSound);
      setIsPlaying(true);

      // Media controls will be handled by persistent notification

      // Note: Media notifications removed due to Expo Go limitations

      // Haptic feedback
      if (Haptics) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
    } catch (error) {
      console.warn("Stream start error:", error);
      setIsPlaying(false);
      setConnectionError(true);
      setManuallyPaused(false); // ✅ هذا خطأ في الشبكة، ليس إيقاف يدوي

      // Note: Error notifications removed due to Expo Go limitations

      showCustomModal(
        "Bağlantı Hatası",
        "Radyo çalınamıyor. İnternet bağlantınızı kontrol edin ve tekrar deneyin.",
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  }, [isLoading, sound, showPlayer, fadeOutAnim, fadeInAnim, STREAM_URL]);

  // Toggle playback with error handling - optimized for speed
  const togglePlayback = useCallback(async () => {
    if (!sound) {
      handleStart();
      startSloganAnimation();
      return;
    }

    try {
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        if (status.isPlaying) {
          // Immediately update UI state for better responsiveness
          setIsPlaying(false);
          setManuallyPaused(true);

          // Then pause the actual sound
          await sound.pauseAsync();

          // Note: Notification update will be added in production build

          if (Haptics) {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }
        } else {
          // Immediately update UI state for better responsiveness
          setIsPlaying(true);
          setManuallyPaused(false);

          // Then play the actual sound
          await sound.playAsync();

          if (Haptics) {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          }
        }
      } else {
        // Sound is not loaded, restart
        handleStart();
      }
    } catch (error) {
      console.warn("Playback toggle error:", error);
      setConnectionError(true);
      setManuallyPaused(false); // ✅ هذا خطأ في الشبكة، ليس إيقاف يدوي
      handleStart();
    }
  }, [sound, handleStart]);

  // Individual random waveform - each bar animates independently
  const renderWaveform = useCallback(() => {
    return (
      <View style={styles.waveform}>
        {[...Array(WAVE_BAR_COUNT)].map((_, i) => {
          // Base heights for each bar
          const baseHeights = [
            20, 15, 35, 25, 30, 18, 40, 28, 22, 38, 32, 26, 42, 24, 36, 20, 30,
            34, 16, 28,
          ];
          const baseHeight = baseHeights[i % baseHeights.length];

          // Each bar has its own individual animation
          const animatedOpacity = barAnimations[i].interpolate({
            inputRange: [0.3, 1],
            outputRange: [0.4, 1], // Opacity from 40% to 100%
            extrapolate: "clamp",
          });

          return (
            <Animated.View
              key={i}
              style={[
                styles.waveBar,
                {
                  height: baseHeight,
                  opacity: isPlaying ? animatedOpacity : 0.3,
                },
              ]}
            />
          );
        })}
      </View>
    );
  }, [barAnimations, isPlaying]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="transparent"
        translucent
      />

      {/* Enhanced Background */}
      <BlurView intensity={80} style={StyleSheet.absoluteFill} tint="dark" />
      <Svg height="100%" width="100%" style={StyleSheet.absoluteFillObject}>
        <Defs>
          <RadialGradient id="grad" cx="50%" cy="30%" rx="100%" ry="80%">
            <Stop offset="0%" stopColor="#9668EF" stopOpacity="0.8" />
            <Stop offset="50%" stopColor="#7C4DFF" stopOpacity="0.6" />
            <Stop offset="100%" stopColor="#0A0A0A" stopOpacity="1" />
          </RadialGradient>
        </Defs>
        <Rect width="100%" height="100%" fill="url(#grad)" />
      </Svg>

      {/* Onboarding Screen */}
      {!showPlayer && (
        <Animated.View style={[styles.content, { opacity: fadeOutAnim }]}>
          <Animated.Image
            source={headphonesImg}
            style={[
              styles.headphones,
              {
                transform: [
                  {
                    translateY: headphoneAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0, -15],
                    }),
                  },
                ],
              },
            ]}
            resizeMode="contain"
          />

          <Text style={styles.bigTitle}>Yakamoz Dinle</Text>
          <Text style={styles.subtitle}>Yakamoz Dinlemeye Hazırmısınız?!</Text>

          <TouchableOpacity
            style={[styles.startButton, isLoading && styles.buttonDisabled]}
            onPress={handleStart}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={isLoading ? ["#666", "#444"] : ["#a18aff", "#7C4DFF"]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.gradientButton}
            >
              <Text style={styles.buttonText}>
                {isLoading ? "Yükleniyor..." : "Dinlemeye Başla"}
              </Text>
              <Ionicons
                name={isLoading ? "hourglass" : "musical-notes"}
                size={22}
                color="#fff"
                style={{ marginLeft: 8 }}
              />
            </LinearGradient>
          </TouchableOpacity>

          <View style={styles.footer}>
            <Text style={styles.footerTitle}>M.B.Z.</Text>
            <Text style={styles.footerSubtitle}>yakamoz music app</Text>
          </View>
        </Animated.View>
      )}

      {/* Player Screen */}
      {showPlayer && (
        <View style={styles.playerContainer}>
          {/* Status and Control Buttons */}
          <View style={styles.topButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.circleButton,
                connectionError && styles.errorButton,
              ]}
              onPress={handleStart}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <Ionicons
                name={isLoading ? "hourglass" : "reload"}
                size={20}
                color="#FFFFFF"
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.circleButton,
                autoReconnectEnabled
                  ? styles.activeButton
                  : styles.inactiveButton,
              ]}
              onPress={() => setAutoReconnectEnabled(!autoReconnectEnabled)}
              activeOpacity={0.7}
            >
              <Ionicons
                name={autoReconnectEnabled ? "wifi" : "cloud-offline"}
                size={18}
                color="#FFFFFF"
              />
            </TouchableOpacity>
          </View>

          {/* Album Art */}
          <View style={styles.artworkContainer}>
            <Image source={radioImg} style={styles.playerArtwork} />
            {connectionError && (
              <View style={styles.errorOverlay}>
                <Ionicons name="alert-circle" size={32} color="#FF6B6B" />
                <Text style={styles.errorText}>Bağlantı Hatası</Text>
              </View>
            )}
            {isPlaying && (
              <View style={styles.playingIndicator}>
                <View style={styles.playingDot} />
                <Text style={styles.liveText}>CANLI</Text>
              </View>
            )}
          </View>

          {/* Slogan Şeridi - Optimized */}
          <View style={styles.sloganContainer}>
            <LinearGradient
              colors={[
                "rgba(124, 77, 255, 0.15)",
                "rgba(124, 77, 255, 0.08)",
                "rgba(124, 77, 255, 0.15)",
              ]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.sloganGradient}
            >
              <Animated.View
                style={[
                  styles.sloganStrip,
                  {
                    transform: [{ translateX: sloganScrollAnim }],
                  },
                ]}
                removeClippedSubviews={true} // Performance optimization
              >
                <Text
                  style={styles.sloganText}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                  adjustsFontSizeToFit={false}
                >
                  {SLOGANS[currentSloganIndex]}
                </Text>
              </Animated.View>
            </LinearGradient>
          </View>

          {/* Track Info */}
          <View style={styles.trackInfo}>
            <Text style={styles.trackTitle}>Radiyo Yakamoz</Text>
            <Text style={styles.trackSubtitle}>
              {isLoading
                ? "Yükleniyor..."
                : connectionError
                ? autoReconnectEnabled
                  ? `Yeniden bağlanıyor... (${retryCount + 1})`
                  : "Bağlantı Hatası"
                : isPlaying
                ? "Canlı Yayın"
                : "Duraklatıldı"}
            </Text>
          </View>

          {/* Waveform */}
          <View style={styles.progressContainer}>{renderWaveform()}</View>

          {/* Playback Controls */}
          <View style={styles.controlsRow}>
            <TouchableOpacity
              onPress={handleStart}
              style={styles.iconButton}
              disabled={isLoading}
              activeOpacity={0.7}
            >
              <Ionicons name="refresh" size={32} color="#FFFFFF" />
            </TouchableOpacity>

            <Animated.View
              style={[
                styles.playButtonContainer,
                { transform: [{ scale: pulseAnim }] },
              ]}
            >
              <View
                style={[
                  styles.playButtonBackground,
                  isPlaying && styles.playingBackground,
                ]}
              />
              <TouchableOpacity
                onPress={togglePlayback}
                disabled={isLoading}
                activeOpacity={0.8}
              >
                <Ionicons
                  name={isLoading ? "hourglass" : isPlaying ? "pause" : "play"}
                  size={48}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            </Animated.View>

            <TouchableOpacity
              onPress={() =>
                showCustomModal(
                  "Bilgilendirme",
                  "35 yıllık tecrübesi ile  92,9 Mhz frekansında Osmaniye merkezli radyomuz KMG araştırmalarında yıllarca birçok katagoride sıralamlara girmiş bir marka radyo istasyonudur.",
                  "info"
                )
              }
              style={styles.iconButton}
              activeOpacity={0.7}
            >
              <Ionicons name="information-circle" size={32} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Custom Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={hideCustomModal}
      >
        <View style={styles.modalOverlay}>
          <BlurView
            intensity={20}
            style={StyleSheet.absoluteFill}
            tint="dark"
          />
          <Animated.View
            style={[
              styles.modalContainer,
              {
                transform: [{ scale: modalScaleAnim }],
              },
            ]}
          >
            <BlurView
              intensity={40}
              style={StyleSheet.absoluteFill}
              tint="dark"
            />

            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Ionicons
                name={
                  modalType === "error"
                    ? "alert-circle"
                    : modalType === "success"
                    ? "checkmark-circle"
                    : "information-circle"
                }
                size={32}
                color={
                  modalType === "error"
                    ? "#FF6B6B"
                    : modalType === "success"
                    ? "#4CAF50"
                    : "#7C4DFF"
                }
              />
              <Text style={styles.modalTitle}>{modalTitle}</Text>
            </View>

            {/* Modal Content */}
            <Text style={styles.modalMessage}>{modalMessage}</Text>

            {/* Modal Button */}
            <TouchableOpacity
              style={[
                styles.modalButton,
                modalType === "error" && styles.modalButtonError,
                modalType === "success" && styles.modalButtonSuccess,
              ]}
              onPress={hideCustomModal}
              activeOpacity={0.8}
            >
              <LinearGradient
                colors={
                  modalType === "error"
                    ? ["#FF6B6B", "#E53E3E"]
                    : modalType === "success"
                    ? ["#4CAF50", "#388E3C"]
                    : ["#a18aff", "#7C4DFF"]
                }
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.modalButtonGradient}
              >
                <Text style={styles.modalButtonText}>Tamam</Text>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  waveContainer: {
    position: "absolute",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 0,
  },
  headphones: {
    width: width * 0.55,
    height: width * 0.55,
    marginBottom: 30,
    zIndex: 1,
  },
  bigTitle: {
    color: "#fff",
    fontSize: 32,
    fontWeight: "bold",
    marginTop: 10,
    textAlign: "center",
  },
  subtitle: {
    color: "#d1cfff",
    fontSize: 16,
    marginTop: 6,
    marginBottom: 40,
    textAlign: "center",
    paddingHorizontal: 20,
  },
  startButton: {
    marginTop: 10,
    borderRadius: 25,
    overflow: "hidden",
    shadowColor: "#7C4DFF",
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 15,
    elevation: 8,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  gradientButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 36,
    paddingVertical: 14,
    borderRadius: 25,
  },
  buttonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
  footer: {
    position: "absolute",
    bottom: 40,
    width: "100%",
    alignItems: "center",
  },
  footerTitle: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 18,
    marginBottom: 2,
    letterSpacing: 1,
  },
  footerSubtitle: {
    color: "#d1cfff",
    fontSize: 13,
    opacity: 0.8,
  },
  playerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  artworkContainer: {
    position: "relative",
    marginBottom: 10, // Reduced to make space for slogan
    alignItems: "center",
  },
  playerArtwork: {
    width: 280,
    height: 280,
    borderRadius: 16,
    alignSelf: "center",
  },
  playingIndicator: {
    position: "absolute",
    top: 15,
    right: 15,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(76, 175, 80, 0.9)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  playingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "#fff",
    marginRight: 5,
  },
  liveText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
    letterSpacing: 0.5,
  },

  errorOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.8)",
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#FF6B6B",
    fontSize: 14,
    marginTop: 8,
    textAlign: "center",
    fontWeight: "500",
  },
  // Slogan şeridi stilleri - optimized
  sloganContainer: {
    width: "100%",
    height: 35, // Slightly smaller for better performance
    marginBottom: 15,
    overflow: "hidden",
    borderRadius: 18,
    justifyContent: "center",
    borderWidth: 1,
    borderColor: "rgba(124, 77, 255, 0.3)",
    // Reduced shadows for better performance
    shadowColor: "#7C4DFF",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  sloganGradient: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    borderRadius: 20,
  },
  sloganStrip: {
    position: "absolute",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    minWidth: width * 2, // Make it wider to show full text
    width: "auto",
  },
  sloganText: {
    color: "#FFFFFF",
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
    letterSpacing: 0.5,
    textShadowColor: "rgba(124, 77, 255, 0.7)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    // Allow full text width
    includeFontPadding: false,
    textAlignVertical: "center",
    flexShrink: 0, // Don't shrink text
    width: "auto",
    minWidth: width * 1.5, // Ensure enough space for full text
  },

  trackInfo: {
    alignItems: "center",
    marginBottom: 0,
  },
  trackTitle: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 5,
  },
  trackSubtitle: {
    color: "rgba(255,255,255,0.8)",
    fontSize: 18,
  },
  progressContainer: {
    width: "100%",
    marginVertical: 5,
  },
  waveform: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-end",
    height: 60,
    width: "100%",
    paddingHorizontal: 10,
  },
  waveBar: {
    width: 3,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
    marginHorizontal: 1,
  },
  controlsRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 40,
    gap: 32,
  },
  iconButton: {
    padding: 8,
  },
  playButtonContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 24,
  },
  playButtonBackground: {
    position: "absolute",
    width: 80,
    height: 80,
    borderRadius: 40,
    opacity: 0.8,
    backgroundColor: "#7C4DFF",
    shadowColor: "#7C4DFF",
    shadowOpacity: 0.6,
    shadowRadius: 20,
    shadowOffset: { width: 0, height: 0 },
  },
  playingBackground: {
    backgroundColor: "#4CAF50",
    shadowColor: "#4CAF50",
  },
  topButtonsContainer: {
    position: "absolute",
    top: 20,
    left: 20,
    zIndex: 10,
  },
  circleButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "rgba(124, 77, 255, 0.8)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  errorButton: {
    backgroundColor: "rgba(255, 107, 107, 0.8)",
  },
  activeButton: {
    backgroundColor: "rgba(76, 175, 80, 0.8)",
  },
  inactiveButton: {
    backgroundColor: "rgba(158, 158, 158, 0.8)",
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  modalContainer: {
    width: width * 0.85,
    backgroundColor: "rgba(16, 10, 28, 0.95)",
    borderRadius: 20,
    padding: 24,
    alignItems: "center",
    shadowColor: "#7C4DFF",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 10,
    borderWidth: 1,
    borderColor: "rgba(124, 77, 255, 0.3)",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  modalTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "bold",
    marginLeft: 12,
    textAlign: "center",
  },
  modalMessage: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 24,
  },
  modalButton: {
    width: "100%",
    borderRadius: 12,
    overflow: "hidden",
  },
  modalButtonError: {
    shadowColor: "#FF6B6B",
  },
  modalButtonSuccess: {
    shadowColor: "#4CAF50",
  },
  modalButtonGradient: {
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: "center",
    justifyContent: "center",
  },
  modalButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
});

export default App;
